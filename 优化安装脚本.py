#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
🚀 图片处理优化安装脚本 🚀
功能: 自动安装 Pillow-SIMD 以提升图片处理性能 4-6 倍
"""

import subprocess
import sys
import os
import logging
from datetime import datetime

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(f'优化安装日志_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log', encoding='utf-8')
        ]
    )

def check_current_pillow():
    """检查当前 Pillow 版本"""
    try:
        from PIL import __version__
        logging.info(f"当前 Pillow 版本: {__version__}")
        
        # 检查是否是 SIMD 版本
        if '.post' in __version__:
            logging.info("✅ 已安装 Pillow-SIMD 优化版本")
            return True, __version__
        else:
            logging.info("⚠️ 当前使用标准 Pillow，可以升级到 SIMD 版本")
            return False, __version__
    except ImportError:
        logging.error("❌ 未安装 Pillow")
        return False, None

def install_pillow_simd():
    """安装 Pillow-SIMD"""
    try:
        logging.info("开始安装 Pillow-SIMD...")
        
        # 步骤1: 卸载现有的 Pillow
        logging.info("步骤 1/3: 卸载现有 Pillow...")
        subprocess.run([sys.executable, "-m", "pip", "uninstall", "pillow", "-y"], 
                      check=True, capture_output=True, text=True)
        logging.info("✅ 已卸载现有 Pillow")
        
        # 步骤2: 安装 Pillow-SIMD
        logging.info("步骤 2/3: 安装 Pillow-SIMD...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "pillow-simd"], 
                               capture_output=True, text=True)
        
        if result.returncode == 0:
            logging.info("✅ Pillow-SIMD 安装成功")
        else:
            logging.warning("⚠️ Pillow-SIMD 安装失败，尝试备用方案...")
            logging.info("错误信息: " + result.stderr)
            
            # 备用方案：从预编译包安装
            logging.info("尝试从预编译包安装...")
            subprocess.run([sys.executable, "-m", "pip", "install", 
                           "--upgrade", "--force-reinstall", "pillow-simd"], 
                          check=True, capture_output=True, text=True)
            logging.info("✅ 从预编译包安装成功")
        
        # 步骤3: 验证安装
        logging.info("步骤 3/3: 验证安装...")
        is_simd, version = check_current_pillow()
        
        if is_simd:
            logging.info(f"🎉 Pillow-SIMD v{version} 安装成功！")
            logging.info("图片处理性能将提升 4-6 倍")
            return True
        else:
            logging.error("❌ 安装验证失败")
            return False
            
    except subprocess.CalledProcessError as e:
        logging.error(f"❌ 安装过程出错: {e}")
        logging.error(f"错误输出: {e.stderr if hasattr(e, 'stderr') else '无详细信息'}")
        return False
    except Exception as e:
        logging.error(f"❌ 未知错误: {e}")
        return False

def install_additional_optimizations():
    """安装其他优化库"""
    try:
        logging.info("安装额外的优化库...")
        
        # 安装 numpy (如果没有的话)
        try:
            import numpy
            logging.info("✅ NumPy 已安装")
        except ImportError:
            logging.info("安装 NumPy...")
            subprocess.run([sys.executable, "-m", "pip", "install", "numpy"], 
                          check=True, capture_output=True, text=True)
            logging.info("✅ NumPy 安装成功")
        
        return True
        
    except Exception as e:
        logging.warning(f"⚠️ 安装额外优化库时出错: {e}")
        return False

def performance_test():
    """简单的性能测试"""
    try:
        logging.info("进行简单的性能测试...")
        
        from PIL import Image
        import time
        import tempfile
        import os
        
        # 创建测试图片
        test_img = Image.new('RGB', (2000, 2000), color='red')
        
        with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp:
            test_img.save(tmp.name, quality=95)
            tmp_path = tmp.name
        
        try:
            # 测试调整大小性能
            start_time = time.time()
            for _ in range(10):
                with Image.open(tmp_path) as img:
                    resized = img.resize((1000, 1000), Image.Resampling.LANCZOS)
            
            end_time = time.time()
            elapsed = end_time - start_time
            
            logging.info(f"性能测试结果: 处理 10 张 2000x2000 图片耗时 {elapsed:.2f} 秒")
            logging.info(f"平均每张图片处理时间: {elapsed/10:.3f} 秒")
            
        finally:
            # 清理测试文件
            if os.path.exists(tmp_path):
                os.unlink(tmp_path)
        
        return True
        
    except Exception as e:
        logging.warning(f"⚠️ 性能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 图片处理优化安装脚本")
    print("=" * 50)
    
    setup_logging()
    
    # 检查当前状态
    is_simd, current_version = check_current_pillow()
    
    if is_simd:
        print(f"✅ 您已经安装了 Pillow-SIMD v{current_version}")
        print("无需重复安装。")
        
        # 进行性能测试
        if input("是否进行性能测试？(y/n): ").lower() == 'y':
            performance_test()
        
        return
    
    # 询问是否安装
    print(f"\n当前使用标准 Pillow v{current_version}")
    print("安装 Pillow-SIMD 可以将图片处理速度提升 4-6 倍")
    
    if input("\n是否安装 Pillow-SIMD？(y/n): ").lower() != 'y':
        print("安装已取消")
        return
    
    # 开始安装
    print("\n开始安装优化版本...")
    
    success = install_pillow_simd()
    
    if success:
        print("\n🎉 安装成功！")
        
        # 安装额外优化
        install_additional_optimizations()
        
        # 性能测试
        if input("\n是否进行性能测试？(y/n): ").lower() == 'y':
            performance_test()
        
        print("\n✅ 优化完成！重启程序后即可享受更快的图片处理速度。")
    else:
        print("\n❌ 安装失败，请查看日志文件了解详细信息")
        print("您仍可以使用标准 Pillow 版本")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户取消安装")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        logging.error(f"程序异常: {e}")
    
    input("\n按回车键退出...")
