#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
📊 图片处理性能测试脚本 📊
功能: 测试不同图片处理方法的性能差异
"""

import time
import os
import tempfile
import logging
from PIL import Image, ImageOps
import gc

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(message)s'
    )

def check_pillow_version():
    """检查 Pillow 版本"""
    try:
        from PIL import __version__
        is_simd = '.post' in __version__
        return is_simd, __version__
    except:
        return False, "未知版本"

def create_test_images(count=5, sizes=[(1920, 1080), (2560, 1440), (3840, 2160)]):
    """创建测试图片"""
    test_files = []
    temp_dir = tempfile.mkdtemp()
    
    logging.info(f"创建 {count * len(sizes)} 张测试图片...")
    
    for size in sizes:
        for i in range(count):
            # 创建彩色测试图片
            img = Image.new('RGB', size, color=(
                (i * 50) % 255, 
                (i * 80) % 255, 
                (i * 120) % 255
            ))
            
            # 添加一些复杂度
            for x in range(0, size[0], 100):
                for y in range(0, size[1], 100):
                    if (x + y) % 200 == 0:
                        # 绘制一些方块增加复杂度
                        for dx in range(50):
                            for dy in range(50):
                                if x + dx < size[0] and y + dy < size[1]:
                                    img.putpixel((x + dx, y + dy), (255, 255, 255))
            
            filename = os.path.join(temp_dir, f"test_{size[0]}x{size[1]}_{i}.jpg")
            img.save(filename, quality=95)
            test_files.append((filename, size))
    
    logging.info(f"测试图片已创建在: {temp_dir}")
    return test_files, temp_dir

def test_resize_performance(test_files, target_size=(1920, 1080)):
    """测试调整大小性能"""
    logging.info(f"开始测试调整大小性能 (目标尺寸: {target_size})")
    
    results = {
        'stretch': [],
        'fit': [],
        'pad': []
    }
    
    for mode in ['stretch', 'fit', 'pad']:
        logging.info(f"测试模式: {mode}")
        start_time = time.time()
        processed_count = 0
        
        for file_path, original_size in test_files:
            try:
                with Image.open(file_path) as img:
                    if mode == 'stretch':
                        resized = img.resize(target_size, Image.Resampling.LANCZOS)
                    elif mode == 'fit':
                        resized = ImageOps.fit(img, target_size, Image.Resampling.LANCZOS)
                    elif mode == 'pad':
                        resized = ImageOps.pad(img, target_size, Image.Resampling.LANCZOS, color='white')
                    
                    processed_count += 1
                    
                    # 每处理5张图片清理一次内存
                    if processed_count % 5 == 0:
                        gc.collect()
                        
            except Exception as e:
                logging.error(f"处理 {file_path} 时出错: {e}")
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        results[mode] = {
            'total_time': elapsed,
            'processed_count': processed_count,
            'avg_time_per_image': elapsed / processed_count if processed_count > 0 else 0,
            'images_per_second': processed_count / elapsed if elapsed > 0 else 0
        }
        
        logging.info(f"{mode} 模式结果:")
        logging.info(f"  总耗时: {elapsed:.2f} 秒")
        logging.info(f"  处理图片: {processed_count} 张")
        logging.info(f"  平均每张: {elapsed/processed_count:.3f} 秒")
        logging.info(f"  处理速度: {processed_count/elapsed:.1f} 张/秒")
        logging.info("")
    
    return results

def test_memory_usage():
    """简单的内存使用测试"""
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    
    logging.info("内存使用情况:")
    memory_info = process.memory_info()
    logging.info(f"  RSS (物理内存): {memory_info.rss / 1024 / 1024:.1f} MB")
    logging.info(f"  VMS (虚拟内存): {memory_info.vms / 1024 / 1024:.1f} MB")
    
    return memory_info

def cleanup_test_files(test_files, temp_dir):
    """清理测试文件"""
    logging.info("清理测试文件...")
    
    for file_path, _ in test_files:
        try:
            if os.path.exists(file_path):
                os.unlink(file_path)
        except Exception as e:
            logging.warning(f"删除文件 {file_path} 失败: {e}")
    
    try:
        os.rmdir(temp_dir)
        logging.info("测试文件清理完成")
    except Exception as e:
        logging.warning(f"删除临时目录失败: {e}")

def generate_report(results, pillow_info):
    """生成性能报告"""
    report = []
    report.append("=" * 60)
    report.append("📊 图片处理性能测试报告")
    report.append("=" * 60)
    report.append(f"Pillow 版本: {pillow_info[1]} ({'SIMD 优化版' if pillow_info[0] else '标准版'})")
    report.append(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    report.append("🔍 各模式性能对比:")
    report.append("-" * 40)
    
    for mode, data in results.items():
        report.append(f"{mode.upper()} 模式:")
        report.append(f"  总耗时: {data['total_time']:.2f} 秒")
        report.append(f"  处理图片: {data['processed_count']} 张")
        report.append(f"  平均每张: {data['avg_time_per_image']:.3f} 秒")
        report.append(f"  处理速度: {data['images_per_second']:.1f} 张/秒")
        report.append("")
    
    # 找出最快的模式
    fastest_mode = min(results.keys(), key=lambda x: results[x]['total_time'])
    report.append(f"🏆 最快模式: {fastest_mode.upper()}")
    report.append(f"   速度: {results[fastest_mode]['images_per_second']:.1f} 张/秒")
    report.append("")
    
    if not pillow_info[0]:
        report.append("💡 优化建议:")
        report.append("   安装 Pillow-SIMD 可将处理速度提升 4-6 倍")
        report.append("   运行 '优化安装脚本.py' 进行自动安装")
        report.append("")
    
    report.append("=" * 60)
    
    return "\n".join(report)

def main():
    """主函数"""
    print("📊 图片处理性能测试")
    print("=" * 40)
    
    setup_logging()
    
    # 检查 Pillow 版本
    pillow_info = check_pillow_version()
    logging.info(f"检测到 Pillow: {'SIMD 优化版' if pillow_info[0] else '标准版'} v{pillow_info[1]}")
    
    try:
        # 检查内存使用
        logging.info("测试开始前内存状态:")
        initial_memory = test_memory_usage()
        
        # 创建测试图片
        test_files, temp_dir = create_test_images(count=3, sizes=[(1920, 1080), (2560, 1440)])
        
        # 执行性能测试
        results = test_resize_performance(test_files, target_size=(1920, 1080))
        
        # 检查测试后内存使用
        logging.info("测试完成后内存状态:")
        final_memory = test_memory_usage()
        
        # 生成报告
        report = generate_report(results, pillow_info)
        print("\n" + report)
        
        # 保存报告到文件
        report_file = f"性能测试报告_{time.strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        logging.info(f"详细报告已保存到: {report_file}")
        
        # 清理测试文件
        cleanup_test_files(test_files, temp_dir)
        
    except Exception as e:
        logging.error(f"测试过程中出错: {e}")
        raise
    
    logging.info("性能测试完成")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n用户取消测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        logging.error(f"测试异常: {e}")
    
    input("\n按回车键退出...")
