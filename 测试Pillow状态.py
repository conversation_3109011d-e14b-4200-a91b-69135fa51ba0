#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的 Pillow 状态检测脚本
"""

def check_pillow_simd():
    """检测是否使用了 Pillow-SIMD"""
    try:
        from PIL import __version__
        print(f"检测到 PIL 版本: {__version__}")
        # Pillow-SIMD 版本号通常包含 .post 后缀
        if '.post' in __version__:
            return True, __version__
        else:
            return False, __version__
    except ImportError as e:
        print(f"PIL 导入失败: {e}")
        return False, "未安装"
    except Exception as e:
        print(f"检测异常: {e}")
        return False, "未知版本"

def main():
    print("🔍 检测 Pillow 状态...")
    
    is_simd, version = check_pillow_simd()
    
    print(f"Pillow 版本: {version}")
    print(f"类型: {'✅ SIMD 优化版' if is_simd else '⚠️ 标准版'}")
    
    if not is_simd:
        print("\n💡 建议:")
        print("运行 '优化安装脚本.py' 安装 Pillow-SIMD 以获得 4-6 倍性能提升")
    else:
        print("\n🎉 您已经在使用优化版本！")

if __name__ == "__main__":
    main()
